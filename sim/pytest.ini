#=========================================================================
# pytest.ini
#=========================================================================
# Configuration file for py.test.

[pytest]

#-------------------------------------------------------------------------
# configure test collection
#-------------------------------------------------------------------------
# We explicitly do not set a pattern for collecting python classes
# yet. This avoids collecting TestHarness which we use in many of our
# PyMTL unit tests, but eventually we will need to decide on a
# replacement.

python_files     = *_test.py
python_classes   = *Tests
python_functions = test test_*

#-------------------------------------------------------------------------
# default commandline arguments
#-------------------------------------------------------------------------
# By default do not show any traceback. This means by deafult py.test
# gives an overview of the results but not any details. Users can use
# --tb=long to get more information on a failing test. We also display
# error/warnings at the end; otherwise syntax errors won't really show
# up.

addopts = --tb=no -r Ew
