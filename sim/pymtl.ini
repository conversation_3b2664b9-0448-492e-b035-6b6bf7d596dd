#=========================================================================
# pymtl.ini
#=========================================================================
# This is a PyMTL configuration ini file for testing purposes. This file
# will be used to indicate the simulation root directory and provide some
# helpful project-wide PyMTL configurations.

#-------------------------------------------------------------------------
# Placeholder configurations
#-------------------------------------------------------------------------

[placeholder]

# Should PyMTL assume that every placeholder top module name is prefixed
# with the parent directory name?
auto_prefix = yes
